import { OpenAIEmbeddings } from "@langchain/openai";
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { Chroma } from "@langchain/community/vectorstores/chroma";
import { Document } from "langchain/document";

const embeddings = new OpenAIEmbeddings();

export const splitTextIntoChunks = async (text: string) => {
  const splitter = new RecursiveCharacterTextSplitter({
    chunkSize: 1000,
    chunkOverlap: 200,
  });
  const docs = await splitter.createDocuments([text]);
  return docs;
};

export const embedAndStoreDocuments = async (
  docs: Document[],
  collectionName: string
) => {
  await Chroma.fromDocuments(docs, embeddings, {
    collectionName: collectionName,
    url: process.env.CHROMA_DB_URL || "http://localhost:8000",
  });
  console.log(
    `Embedded and stored ${docs.length} documents in Chroma collection ${collectionName}`
  );
};

export const retrieveDocuments = async (
  query: string,
  collectionName: string,
  k: number = 4
) => {
  try {
    // Use OpenAI embeddings to generate query vector
    const queryVector = await embeddings.embedQuery(query);

    // Use direct ChromaDB client for querying with embeddings
    const { queryCollectionWithEmbeddings } = await import("./chromaService");
    const results = await queryCollectionWithEmbeddings(
      collectionName,
      queryVector,
      k
    );

    // Convert ChromaDB results to LangChain Document format
    const documents = [];
    if (results.documents && results.documents[0]) {
      for (let i = 0; i < results.documents[0].length; i++) {
        const content = results.documents[0][i];
        const metadata = results.metadatas?.[0]?.[i] || {};

        documents.push({
          pageContent: content,
          metadata: metadata,
        });
      }
    }

    console.log(`Retrieved ${documents.length} documents for query: ${query}`);
    return documents;
  } catch (error) {
    console.error("Error in retrieveDocuments:", error);

    // If collection doesn't exist or is empty, return empty array
    if (
      error instanceof Error &&
      (error.message.includes("Collection") ||
        error.message.includes("where clause") ||
        error.message.includes("not found"))
    ) {
      console.log(
        `Collection ${collectionName} might be empty or not exist. Returning empty results.`
      );
      return [];
    }

    // Re-throw other errors
    throw error;
  }
};
