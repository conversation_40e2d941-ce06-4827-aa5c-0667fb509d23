import { Router } from "express";
import { retrieveDocuments } from "../services/langchainService";
import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";

const router = Router();

router.post("/rag", async (req, res) => {
  const { question } = req.body;

  if (!question) {
    return res.status(400).send("Question is required.");
  }

  try {
    const collectionName = "mwanrag"; // Should match the collection name used in process.ts
    const relevantDocs = await retrieveDocuments(question, collectionName);

    const context = relevantDocs.map((doc) => doc.pageContent).join("\n\n");

    const chat = new ChatOpenAI({
      model: "gpt-4o", // Using gpt-4o as a placeholder for Gemini, as Gemini is not directly supported by ChatOpenAI
      temperature: 0.7,
    });

    const response = await chat.invoke([
      new SystemMessage(
        `You are a helpful AI assistant. Use the following context to answer the user's question:\n\n${context}`
      ),
      new HumanMessage(question),
    ]);

    res.status(200).json({ answer: response.content });
  } catch (error) {
    console.error("Error in RAG endpoint:", error);
    res.status(500).send("Error processing RAG request.");
  }
});

export default router;
