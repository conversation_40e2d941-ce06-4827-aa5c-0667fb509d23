import { Router } from "express";
import { retrieveDocuments } from "../services/langchainService";
import {
  getAllDocumentsFromCollection,
  queryCollection,
} from "../services/chromaService";

const router = Router();

router.get("/query", async (req, res) => {
  const { q } = req.query;

  if (!q || typeof q !== "string") {
    return res.status(400).send('Query parameter "q" is required.');
  }

  try {
    const collectionName = "mwanrag"; // Should match the collection name used in process.ts
    const relevantDocs = await retrieveDocuments(q, collectionName);
    res.status(200).json(relevantDocs);
  } catch (error) {
    console.error("Error querying documents:", error);
    res.status(500).send("Error querying documents.");
  }
});

router.get("/all-data", async (req, res) => {
  try {
    const collectionName = "mwanrag"; // Should match the collection name used in process.ts
    const allData = await getAllDocumentsFromCollection(collectionName);

    if (allData.count === 0) {
      return res.status(200).json({
        success: true,
        message: "No documents found in the collection.",
        data: "",
        count: 0,
        collectionName: collectionName,
      });
    }
    console.log(allData.documents.slice(0, 10));

    // Combine all documents into a single text string
    const allText = allData.documents
      .filter((doc) => doc !== null) // Filter out null documents
      .map((doc, index) => {
        const metadata = allData.metadatas[index];
        const filename = metadata?.filename || "unknown";
        return `--- Document from ${filename} ---\n${doc}\n`;
      })
      .join("\n");

    res.status(200).json({
      success: true,
      message: `Retrieved all data from collection. Total documents: ${allData.count}`,
      data: allText,
      count: allData.count,
      collectionName: collectionName,
      documentSources: allData.metadatas
        .filter((meta) => meta !== null)
        .map((meta) => meta?.filename)
        .filter((filename, index, arr) => arr.indexOf(filename) === index), // Remove duplicates
    });
  } catch (error) {
    console.error("Error retrieving all documents:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving all documents.",
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

router.get("/search", async (req, res) => {
  const { q } = req.query;

  if (!q || typeof q !== "string") {
    return res.status(400).json({
      success: false,
      message: 'Query parameter "q" is required.',
    });
  }

  try {
    const collectionName = "mwanrag";
    const results = await queryCollection(collectionName, [q], 5);

    if (
      !results.documents ||
      results.documents.length === 0 ||
      !results.documents[0] ||
      results.documents[0].length === 0
    ) {
      return res.status(200).json({
        success: true,
        message: "No relevant documents found for your query.",
        query: q,
        results: [],
        count: 0,
      });
    }

    // Format the results
    const formattedResults = results.documents[0].map((doc, index) => {
      const metadata = results.metadatas?.[0]?.[index];
      const distance = results.distances?.[0]?.[index];

      return {
        content: doc,
        metadata: metadata || {},
        similarity: distance ? 1 - distance : null, // Convert distance to similarity
        source: metadata?.filename || "unknown",
      };
    });

    res.status(200).json({
      success: true,
      message: `Found ${formattedResults.length} relevant documents.`,
      query: q,
      results: formattedResults,
      count: formattedResults.length,
    });
  } catch (error) {
    console.error("Error in search endpoint:", error);
    res.status(500).json({
      success: false,
      message: "Error searching documents.",
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

export default router;
